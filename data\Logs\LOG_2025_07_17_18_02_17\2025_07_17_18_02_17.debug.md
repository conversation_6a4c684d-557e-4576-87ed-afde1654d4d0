2025-07-17 18:02:17.756 [00000000000028bc] [-] Start file-log 'warn'
2025-07-17 18:02:17.757 [00000000000028bc] [I] +++ start Logger (StartLogging@Logger\mLogger.cpp:246)
2025-07-17 18:02:17.757 [00000000000028bc] [!] L_FATAL (StartLogging@Logger\mLogger.cpp:247)
2025-07-17 18:02:17.757 [00000000000028bc] [E] L_ERROR (StartLogging@Logger\mLogger.cpp:248)
2025-07-17 18:02:17.758 [00000000000028bc] [W] L_WARN (StartLogging@Logger\mLogger.cpp:249)
2025-07-17 18:02:17.758 [00000000000028bc] [N] L_NOTE (StartLogging@Logger\mLogger.cpp:250)
2025-07-17 18:02:17.758 [00000000000028bc] [I] L_INFO (StartLogging@Logger\mLogger.cpp:251)
2025-07-17 18:02:17.758 [00000000000028bc] [I] --- start Logger (StartLogging@Logger\mLogger.cpp:253)
2025-07-17 18:02:17.758 [00000000000028bc] [W] Running SamUniSoCTool - SUT - V1.20 (StartLogging@Logger\mLogger.cpp:254)
2025-07-17 18:02:17.759 [00000000000028bc] [N] Start (StartLogging@Logger\mLogger.cpp:256)
2025-07-17 18:02:17.759 [00000000000028bc] [I] WinsCard::Notfound (connect_mf_card@mfapi\mft.server.cpp:433)
2025-07-17 18:02:17.759 [00000000000028bc] [W] qbool mrx::qCheckVSCard() (qCheckVSCard@security\mrxsec.cpp:364)
2025-07-17 18:04:29.816 [00000000000028bc] [W] dtor of virtual MainWin::~MainWin() (~<EMAIL>:1444)
2025-07-17 18:04:29.816 [00000000000028bc] [I] qvoid MainWin::ClearObjects() (<EMAIL>:1457)
2025-07-17 18:04:29.816 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_loader (<EMAIL>:1459)
2025-07-17 18:04:29.816 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_loader:ok (<EMAIL>:1466)
2025-07-17 18:04:29.816 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_callback (<EMAIL>:1467)
2025-07-17 18:04:29.816 [00000000000028bc] [W] dtor of virtual mrx::~mrx() (~mrx@mThread\m_callback.cpp:39)
2025-07-17 18:04:29.816 [00000000000028bc] [W] virtual mrx::~mrx()::m_scard ok! (~mrx@mThread\m_callback.cpp:47)
2025-07-17 18:04:29.816 [00000000000028bc] [W] virtual mrx::~mrx()::mseclib ok! (~mrx@mThread\m_callback.cpp:55)
2025-07-17 18:04:29.816 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_callback:ok (<EMAIL>:1474)
2025-07-17 18:04:29.816 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_tabs_bar (<EMAIL>:1475)
2025-07-17 18:04:29.817 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_tabs_bar:ok (<EMAIL>:1482)
2025-07-17 18:04:29.817 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_mprg_bar (<EMAIL>:1483)
2025-07-17 18:04:29.817 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_mprg_bar:ok (<EMAIL>:1490)
2025-07-17 18:04:29.817 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_btn_stop (<EMAIL>:1491)
2025-07-17 18:04:29.817 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_btn_stop:ok (<EMAIL>:1498)
2025-07-17 18:04:29.817 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_btn_snap (<EMAIL>:1499)
2025-07-17 18:04:29.818 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_btn_snap:ok (<EMAIL>:1506)
2025-07-17 18:04:29.818 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_btn_about (<EMAIL>:1507)
2025-07-17 18:04:29.819 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_btn_about:ok (<EMAIL>:1514)
2025-07-17 18:04:29.819 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::m_btn_dmgr (<EMAIL>:1515)
2025-07-17 18:04:29.819 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::m_btn_dmgr:ok (<EMAIL>:1522)
2025-07-17 18:04:29.819 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::m_status_lb (<EMAIL>:1523)
2025-07-17 18:04:29.819 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_status_lb:ok (<EMAIL>:1530)
2025-07-17 18:04:29.819 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_custom_da_path (<EMAIL>:1531)
2025-07-17 18:04:29.819 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_custom_da_path:ok (<EMAIL>:1538)
2025-07-17 18:04:29.819 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_m_chk_custom_daa (<EMAIL>:1539)
2025-07-17 18:04:29.821 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_chk_custom_daa:ok (<EMAIL>:1546)
2025-07-17 18:04:29.821 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_chk_custom_bmda (<EMAIL>:1547)
2025-07-17 18:04:29.821 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_chk_custom_bmd:ok (<EMAIL>:1554)
2025-07-17 18:04:29.821 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_chk_custom_bnd (<EMAIL>:1555)
2025-07-17 18:04:29.821 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_chk_custom_bnd:ok (<EMAIL>:1562)
2025-07-17 18:04:29.821 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_chk_custom_mdn (<EMAIL>:1563)
2025-07-17 18:04:29.821 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::free_chk_custom_mdn:ok (<EMAIL>:1570)
2025-07-17 18:04:29.821 [00000000000028bc] [I] qvoid MainWin::ClearObjects()::chk_custom_mem:ok (<EMAIL>:1577)
2025-07-17 18:04:29.822 [00000000000028bc] [I] qvoid MainWin::ClearObjects() ok! (<EMAIL>:1579)
